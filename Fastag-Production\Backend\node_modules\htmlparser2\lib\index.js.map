{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAoD;AACpD,yCAAyD;AAAhD,mGAAA,MAAM,OAAA;AAEf,yCAMoB;AAEpB,yCAKoB;AAJhB,wGAAA,UAAU,OAAA;AACV,0BAA0B;AAC1B,4GAAA,UAAU,OAAkB;AAMhC,iBAAiB;AAEjB;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,IAAY,EAAE,OAAiB;IACzD,IAAM,OAAO,GAAG,IAAI,uBAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACnD,IAAI,kBAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,OAAO,CAAC,IAAI,CAAC;AACxB,CAAC;AAJD,sCAIC;AACD;;;;;;;;;GASG;AACH,SAAgB,QAAQ,CAAC,IAAY,EAAE,OAAiB;IACpD,OAAO,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC;AACjD,CAAC;AAFD,4BAEC;AACD;;;;;;GAMG;AACH,SAAgB,eAAe,CAC3B,QAAyD,EACzD,OAAiB,EACjB,eAA4C;IAE5C,IAAM,OAAO,GAAG,IAAI,uBAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;IACnE,OAAO,IAAI,kBAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAPD,0CAOC;AAED,+CAGwB;AAFpB,0HAAA,OAAO,OAAa;AAIxB;;;GAGG;AACH,8DAA8C;AAE9C,qCAAyC;AAEzC,qCAAmC;AAA1B,mGAAA,OAAO,OAAA;AAEhB,IAAM,uBAAuB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAElD;;;;;GAKG;AACH,SAAgB,SAAS,CACrB,IAAY,EACZ,OAA0C;IAA1C,wBAAA,EAAA,iCAA0C;IAE1C,OAAO,IAAA,kBAAO,EAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5C,CAAC;AALD,8BAKC;AAED,qDAAqC"}