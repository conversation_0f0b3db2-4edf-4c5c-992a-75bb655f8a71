{"version": 3, "file": "Parser.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["Parser.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAuGjE,MAAM,WAAW,aAAa;IAC1B;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;;OAIG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;OAIG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB;;;;OAIG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;IAElC;;;;;OAKG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;;;;OAKG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,SAAS,CAAC;CAChC;AAED,MAAM,WAAW,OAAO;IACpB,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;OAEG;IACH,OAAO,IAAI,IAAI,CAAC;IAEhB;;OAEG;IACH,KAAK,IAAI,IAAI,CAAC;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IAC5B,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC;IACnD,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC;;;;;OAKG;IACH,WAAW,CACP,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,GAClC,IAAI,CAAC;IACR,SAAS,CACL,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,EAChC,SAAS,EAAE,OAAO,GACnB,IAAI,CAAC;IACR,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,YAAY,IAAI,IAAI,CAAC;IACrB,UAAU,IAAI,IAAI,CAAC;IACnB,YAAY,IAAI,IAAI,CAAC;IACrB,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7D;AAID,qBAAa,MAAO,YAAW,SAAS;IA+BhC,OAAO,CAAC,QAAQ,CAAC,OAAO;IA9B5B,yCAAyC;IAClC,UAAU,SAAK;IACtB,uCAAuC;IAChC,QAAQ,SAAK;IACpB;;;OAGG;IACH,OAAO,CAAC,YAAY,CAAK;IAEzB,OAAO,CAAC,OAAO,CAAM;IACrB,OAAO,CAAC,UAAU,CAAM;IACxB,OAAO,CAAC,WAAW,CAAM;IACzB,OAAO,CAAC,OAAO,CAA0C;IACzD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAgB;IACtC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAiB;IAChD,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAmB;IACvC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAU;IAC5C,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAU;IAClD,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAY;IAEtC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAgB;IACxC,OAAO,CAAC,YAAY,CAAK;IACzB,kFAAkF;IAClF,OAAO,CAAC,UAAU,CAAK;IACvB,kFAAkF;IAClF,OAAO,CAAC,KAAK,CAAS;gBAGlB,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,EACZ,OAAO,GAAE,aAAkB;IAehD,gBAAgB;IAChB,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAO7C,gBAAgB;IAChB,YAAY,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAW9B,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAI9C,gBAAgB;IAChB,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAYpD,OAAO,CAAC,WAAW;IA4BnB,OAAO,CAAC,UAAU;IAclB,gBAAgB;IAChB,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAQpC,gBAAgB;IAChB,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IA0CjD,gBAAgB;IAChB,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAiBxC,OAAO,CAAC,eAAe;IAYvB,gBAAgB;IAChB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IASnD,gBAAgB;IAChB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAInD,gBAAgB;IAChB,cAAc,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAIhC,gBAAgB;IAChB,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAwBrD,OAAO,CAAC,kBAAkB;IAW1B,gBAAgB;IAChB,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAapD,gBAAgB;IAChB,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;IAa9D,gBAAgB;IAChB,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAUhE,gBAAgB;IAChB,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAiB9D,gBAAgB;IAChB,KAAK,IAAI,IAAI;IAab;;OAEG;IACI,KAAK,IAAI,IAAI;IAgBpB;;;;;OAKG;IACI,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAKxC,OAAO,CAAC,QAAQ;IAkBhB,OAAO,CAAC,WAAW;IAMnB;;;;OAIG;IACI,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAajC;;;;OAIG;IACI,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAWhC;;OAEG;IACI,KAAK,IAAI,IAAI;IAIpB;;OAEG;IACI,MAAM,IAAI,IAAI;IAarB;;;;;OAKG;IACI,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAGtC;;;;;OAKG;IACI,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;CAGpC"}