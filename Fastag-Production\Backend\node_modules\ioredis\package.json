{"name": "i<PERSON>is", "version": "5.6.1", "description": "A robust, performance-focused and full-featured Redis client for Node.js.", "main": "./built/index.js", "types": "./built/index.d.ts", "files": ["built/"], "scripts": {"test:tsd": "npm run build && tsd", "test:js": "TS_NODE_TRANSPILE_ONLY=true NODE_ENV=test mocha \"test/helpers/*.ts\" \"test/unit/**/*.ts\" \"test/functional/**/*.ts\"", "test:cov": "nyc npm run test:js", "test:js:cluster": "TS_NODE_TRANSPILE_ONLY=true NODE_ENV=test mocha \"test/cluster/**/*.ts\"", "test": "npm run test:js && npm run test:tsd", "lint": "eslint --ext .js,.ts ./lib", "docs": "npx typedoc --logLevel Error --excludeExternals --excludeProtected --excludePrivate --readme none lib/index.ts", "format": "prettier --write \"{,!(node_modules)/**/}*.{js,ts}\"", "format-check": "prettier --check \"{,!(node_modules)/**/}*.{js,ts}\"", "build": "rm -rf built && tsc", "prepublishOnly": "npm run build", "semantic-release": "semantic-release"}, "repository": {"type": "git", "url": "git://github.com/luin/ioredis.git"}, "keywords": ["redis", "cluster", "sentinel", "pipelining"], "tsd": {"directory": "test/typing"}, "author": "Zihua Li <<EMAIL>> (http://zihua.li)", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ioredis"}, "dependencies": {"@ioredis/commands": "^1.1.1", "cluster-key-slot": "^1.1.0", "debug": "^4.3.4", "denque": "^2.1.0", "lodash.defaults": "^4.2.0", "lodash.isarguments": "^3.1.0", "redis-errors": "^1.2.0", "redis-parser": "^3.0.0", "standard-as-callback": "^2.1.0"}, "devDependencies": {"@ioredis/interface-generator": "^1.3.0", "@semantic-release/changelog": "^6.0.1", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@types/chai": "^4.3.0", "@types/chai-as-promised": "^7.1.5", "@types/debug": "^4.1.5", "@types/lodash.defaults": "^4.2.7", "@types/lodash.isarguments": "^3.1.7", "@types/mocha": "^9.1.0", "@types/node": "^14.18.12", "@types/redis-errors": "^1.2.1", "@types/sinon": "^10.0.11", "@typescript-eslint/eslint-plugin": "^5.48.1", "@typescript-eslint/parser": "^5.48.1", "chai": "^4.3.6", "chai-as-promised": "^7.1.1", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "mocha": "^9.2.1", "nyc": "^15.1.0", "prettier": "^2.6.1", "semantic-release": "^19.0.2", "server-destroy": "^1.0.1", "sinon": "^13.0.1", "ts-node": "^10.4.0", "tsd": "^0.19.1", "typedoc": "^0.22.18", "typescript": "^4.6.3", "uuid": "^9.0.0"}, "nyc": {"reporter": ["lcov"]}, "engines": {"node": ">=12.22.0"}, "mocha": {"exit": true, "timeout": 8000, "recursive": true, "require": "ts-node/register"}}