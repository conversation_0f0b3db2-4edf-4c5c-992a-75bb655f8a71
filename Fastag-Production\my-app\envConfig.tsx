const getServerBaseUrl = (): string => {
  // First check if EXPO_PUBLIC_API_URL is set (this is the primary way to configure the API URL)
  if (process.env.EXPO_PUBLIC_API_URL) {
    console.log("Using EXPO_PUBLIC_API_URL:", process.env.EXPO_PUBLIC_API_URL);
    return process.env.EXPO_PUBLIC_API_URL;
  }

  // Fallback to environment-based selection
  const isDevelopment = process.env.NODE_ENV === 'development';
  console.log("node env is ", process.env.NODE_ENV);

  const LOCAL_SERVER_URL = process.env.EXPO_PUBLIC_LOCAL_SERVER_URL || 'http://localhost:5004/';
  const PRODUCTION_SERVER_URL = process.env.EXPO_PUBLIC_PRODUCTION_SERVER_URL || 'https://fastag.bd1.pro/';

  const selectedUrl = isDevelopment ? LOCAL_SERVER_URL : PRODUCTION_SERVER_URL;
  console.log("Selected backend URL:", selectedUrl);

  return selectedUrl;
};

export default getServerBaseUrl;